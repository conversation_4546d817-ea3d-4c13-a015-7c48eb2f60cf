const canvas = document.getElementById("smokeCanvas");
const ctx = canvas.getContext("2d");

// Ensure canvas covers full window
canvas.width = window.innerWidth;
canvas.height = window.innerHeight;

const heroRect = document.getElementById("heroRect");

// Function to update rectangle position (called on resize and initially)
function updateRectPosition() {
  rectY = heroRect.offsetTop;
  rectBottom = heroRect.offsetTop + heroRect.offsetHeight;
}

const rectLeft = () => heroRect.offsetLeft;
const rectRight = () => heroRect.offsetLeft + heroRect.offsetWidth;
const rectCenter = () => heroRect.offsetLeft + heroRect.offsetWidth / 2;

// Initialize rectangle position
let rectY, rectBottom;
updateRectPosition();

const particles = [];

// Violet and purple smoke colors
function getSmokeColor(alpha = 1, intensity = 1, colorType = 'violet') {
  if (colorType === 'violet') {
    const baseR = Math.floor(138 * intensity);
    const baseG = Math.floor(43 * intensity);
    const baseB = Math.floor(226 * intensity);
    return `rgba(${baseR}, ${baseG}, ${baseB}, ${alpha})`;
  } else { // purple
    const baseR = Math.floor(128 * intensity);
    const baseG = Math.floor(0 * intensity);
    const baseB = Math.floor(128 * intensity);
    return `rgba(${baseR}, ${baseG}, ${baseB}, ${alpha})`;
  }
}

function getRandomSmokeColor(alpha = 1, intensity = 1) {
  return Math.random() > 0.5 ?
    getSmokeColor(alpha, intensity, 'violet') :
    getSmokeColor(alpha, intensity, 'purple');
}

class BeamParticle {
  constructor(x, y) {
    this.x = x + (Math.random() - 0.5) * 12;
    this.y = y;
    this.size = Math.random() * 6 + 3;
    this.speedY = Math.random() * 3 + 3;
    this.speedX = (Math.random() - 0.5) * 0.4;
    this.alpha = 0.9 + Math.random() * 0.1;
    this.life = 100;
    this.maxLife = this.life;
    this.intensity = Math.random() * 0.6 + 0.6;
    this.colorType = Math.random() > 0.5 ? 'violet' : 'purple';
  }

  update() {
    if (this.y < rectY - 15) {
      this.y += this.speedY;
      this.x += this.speedX;
      this.speedX *= 0.98;
    } else {
      // Just remove when hitting rectangle - no spreading
      this.life = 0;
    }

    this.life--;
  }

  draw() {
    const gradient = ctx.createRadialGradient(this.x, this.y, 0, this.x, this.y, this.size * 2.5);
    const baseAlpha = this.alpha * (this.life / this.maxLife);

    const smokeColor = getSmokeColor(1, this.intensity, this.colorType);
    const [r, g, b] = smokeColor.match(/\d+/g);

    gradient.addColorStop(0, `rgba(${r}, ${g}, ${b}, ${baseAlpha * 0.8})`);
    gradient.addColorStop(0.3, `rgba(${r}, ${g}, ${b}, ${baseAlpha * 0.6})`);
    gradient.addColorStop(0.6, `rgba(${r}, ${g}, ${b}, ${baseAlpha * 0.3})`);
    gradient.addColorStop(1, `rgba(${r}, ${g}, ${b}, 0)`);

    ctx.fillStyle = gradient;
    ctx.beginPath();
    ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
    ctx.fill();
  }
}

// Concentrated beam that creates the smoke impact
function drawAtomicCoreBeam() {
  const centerX = canvas.width / 2;
  const time = Date.now() * 0.008;
  const pulseIntensity = Math.sin(time) * 0.4 + 0.8;
  const secondaryPulse = Math.sin(time * 1.7) * 0.2 + 0.9;

  // Main concentrated beam core - thinner for more realistic impact
  const coreWidth = 8 * pulseIntensity;
  const coreGradient = ctx.createLinearGradient(centerX, 0, centerX, rectY);
  coreGradient.addColorStop(0, getSmokeColor(0.95 * pulseIntensity, 1.5, 'violet'));
  coreGradient.addColorStop(0.4, getSmokeColor(0.9 * pulseIntensity, 1.3, 'violet'));
  coreGradient.addColorStop(0.8, getSmokeColor(0.7 * pulseIntensity, 1.1, 'purple'));
  coreGradient.addColorStop(1, getSmokeColor(0.3, 0.8, 'purple'));

  ctx.fillStyle = coreGradient;
  ctx.fillRect(centerX - coreWidth / 2, 0, coreWidth, rectY);

  // Outer energy field - smaller for more concentrated beam
  const outerWidth = 20 * secondaryPulse;
  const outerGradient = ctx.createLinearGradient(centerX, 0, centerX, rectY);
  outerGradient.addColorStop(0, getSmokeColor(0.3 * secondaryPulse, 0.9, 'violet'));
  outerGradient.addColorStop(0.5, getSmokeColor(0.2 * secondaryPulse, 0.7, 'purple'));
  outerGradient.addColorStop(1, getSmokeColor(0.05, 0.5, 'violet'));

  ctx.fillStyle = outerGradient;
  ctx.fillRect(centerX - outerWidth / 2, 0, outerWidth, rectY);

  // Reduced crackling for cleaner beam
  for (let i = 0; i < 6; i++) {
    const crackleX = centerX + (Math.random() - 0.5) * 25 * pulseIntensity;
    const crackleY = Math.random() * rectY;
    const crackleSize = Math.random() * 3 + 1;
    const crackleAlpha = Math.random() * 0.5 + 0.3;

    const crackleGradient = ctx.createRadialGradient(crackleX, crackleY, 0, crackleX, crackleY, crackleSize * 2);
    crackleGradient.addColorStop(0, getSmokeColor(crackleAlpha, 1.2, Math.random() > 0.5 ? 'violet' : 'purple'));
    crackleGradient.addColorStop(0.5, getSmokeColor(crackleAlpha * 0.5, 1.0, 'violet'));
    crackleGradient.addColorStop(1, getSmokeColor(0, 0.6, 'purple'));

    ctx.fillStyle = crackleGradient;
    ctx.beginPath();
    ctx.arc(crackleX, crackleY, crackleSize, 0, Math.PI * 2);
    ctx.fill();
  }
}

// Bright glowing border around the single rectangle
function drawRectangleBorder() {
  const rectTop = rectY;
  const rectLeftX = rectLeft();
  const rectRightX = rectRight();
  const rectWidth = rectRightX - rectLeftX;
  const rectHeight = heroRect.offsetHeight;
  const time = Date.now() * 0.008;

  // Pulsing intensity for the glowing border
  const pulseIntensity = Math.sin(time) * 0.3 + 0.7;

  // Bright glowing border around the entire rectangle
  ctx.strokeStyle = getSmokeColor(0.9 * pulseIntensity, 1.2, Math.sin(time * 0.5) > 0 ? 'violet' : 'purple');
  ctx.lineWidth = 3;
  ctx.shadowColor = getSmokeColor(0.8, 1.0, 'violet');
  ctx.shadowBlur = 12;

  // Draw border around the entire rectangle
  ctx.strokeRect(rectLeftX, rectTop, rectWidth, rectHeight);

  // Reset shadow for other drawings
  ctx.shadowBlur = 0;
}

function animate() {
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  drawAtomicCoreBeam();
  drawRectangleBorder();

  // Simple beam particle emission
  for (let i = 0; i < 4; i++) {
    const offsetX = (Math.random() - 0.5) * 15;
    particles.push(new BeamParticle(canvas.width / 2 + offsetX, -30));
  }

  // Update & draw beam particles only
  for (let i = 0; i < particles.length; i++) {
    const p = particles[i];
    p.update();
    p.draw();

    if (p.life <= 0 || p.alpha <= 0) {
      particles.splice(i, 1);
      i--;
    }
  }

  requestAnimationFrame(animate);
}

animate();

window.addEventListener("resize", () => {
  canvas.width = window.innerWidth;
  canvas.height = window.innerHeight;
  updateRectPosition();
});
